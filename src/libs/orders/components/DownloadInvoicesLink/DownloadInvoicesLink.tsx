import { Flex, Text } from '@mantine/core';
import { Button } from '@/libs/ui/Button/Button';
import InvoiceIcon from './assets/invoice.svg?react';

interface DownloadInvoicesLinkProps {
  url: string;
}
export const DownloadInvoicesLink = ({ url }: DownloadInvoicesLinkProps) => (
  <Button
    variant="unstyled"
    href={url}
    style={{ marginBottom: '1rem' }}
    download
  >
    <Flex align="center" gap="0.3rem">
      <InvoiceIcon />
      <Text c="#4942D4" size="xs" fw="500">
        Download Invoices
      </Text>
    </Flex>
  </Button>
);
