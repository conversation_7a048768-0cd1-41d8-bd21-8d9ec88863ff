import { Flex, Text } from '@mantine/core';
import { Button } from '@/libs/ui/Button/Button';
import ClipboardIcon from './assets/clipboard.svg?react';

interface DownloadChecklistProps {
  url: string;
}
export const DownloadChecklist = ({ url }: DownloadChecklistProps) => (
  <Button
    variant="unstyled"
    href={url}
    style={{ marginBottom: '1rem' }}
    download
  >
    <Flex align="center" gap="0.3rem">
      <ClipboardIcon />
      <Text c="#4942D4" size="xs" fw="500">
        Download Checklist
      </Text>
    </Flex>
  </Button>
);
