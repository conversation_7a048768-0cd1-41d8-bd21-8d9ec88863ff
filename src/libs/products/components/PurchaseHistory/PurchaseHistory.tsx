import { Box, Divider, Flex, Modal, Text } from '@mantine/core';
import { Button } from '@/libs/ui/Button/Button';
import styles from './PurchaseHistory.module.css';
import { DEFAULT_DISPLAY_DATE_FORMAT } from '@/constants';
import dayjs from 'dayjs';
import { useDisclosure } from '@mantine/hooks';
import { PurchaseHistoryChart } from './PurchaseHistoryChart';
import LastTimeIcon from './assets/images/last-time.svg?react';

type PurchaseHistoryProps = {
  productId: string;
  lastOrderedQuantity: string | number | null;
  lastOrderedAt: string | null;
};

export const PurchaseHistory = ({
  productId,
  lastOrderedQuantity,
  lastOrderedAt,
}: PurchaseHistoryProps) => {
  const [isModalOpen, { open, close }] = useDisclosure(false);

  if (!lastOrderedAt || !lastOrderedQuantity) {
    return null;
  }

  return (
    <Flex align="center" justify="space-between" className={styles.container}>
      <Flex align="center">
        <LastTimeIcon />
        <Text fw="500" ml="0.375rem" size="0.75rem">
          <span>
            {lastOrderedQuantity} on{' '}
            {dayjs(lastOrderedAt).format(DEFAULT_DISPLAY_DATE_FORMAT)}
          </span>
        </Text>
      </Flex>
      <Divider orientation="vertical" />
      <Button
        variant="unstyled"
        onClick={open}
        style={{ padding: '0.375rem 0' }}
      >
        <Text size="0.75rem" c="#447BFD" fw="500">
          Purchase History
        </Text>
      </Button>
      <Modal
        opened={isModalOpen}
        onClose={close}
        title="Purchase History"
        size="auto"
      >
        <Box p="md">
          <PurchaseHistoryChart productId={productId} />
        </Box>
      </Modal>
    </Flex>
  );
};
