import { Box, Button, Flex } from '@mantine/core';
import { Link } from 'react-router-dom';
import {
  SidebarNav,
  type SidebarNavProps,
} from '@/libs/ui/SidebarNav/SidebarNav';
import { Logo } from '@/libs/ui/Logo/Logo';
import { GPO_ROUTES_PATH } from '@/apps/gpo-portal/routes/routes';

import styles from './GpoSidebar.module.css';
import { HF_CONTACT_EMAIL } from '@/constants';
import SettingsIcon from './assets/settings.svg?react';
import HomeIcon from './assets/home.svg?react';
import QuestionMarkIcon from './assets/question.svg?react';

const GPO_NAV_LINKS: SidebarNavProps[] = [
  {
    title: 'Main menu',
    links: [
      {
        label: 'Home',
        icon: <HomeIcon />,
        path: GPO_ROUTES_PATH.dashboard,
      },
      // {
      //   label: 'Spent Report',
      //   icon: <SpentReportIcon />,
      //   path: '/spent-report',
      // },
      // {
      //   label: 'Platform usage',
      //   icon: <PlatformUsageIcon />,
      //   path: '/platform-usage',
      // },
      // {
      //   label: 'Vendors',
      //   icon: <VendorsIcon />,
      //   path: '/vendors',
      // },
    ],
  },
  {
    title: 'Preferences',
    links: [
      {
        label: 'Settings',
        icon: <SettingsIcon />,
        path: '/settings',
      },
    ],
  },
];

export const GpoSidebar = () => {
  // const { user, logout } = useAuthStore();

  return (
    <div className={styles.sidebarRoot}>
      <Box maw="80%" mah="8rem" mx="auto" mt="3.5rem" mb="1.5rem">
        <Link to={GPO_ROUTES_PATH.dashboard}>
          <Logo />
        </Link>
      </Box>

      <Flex direction="column" h="100%" justify="space-between">
        <Box>
          {GPO_NAV_LINKS.map(({ title, links }) => (
            <SidebarNav key={title} title={title} links={links} theme="dark" />
          ))}

          <Box mt="md">
            <Button
              component="a"
              href={`mailto:${HF_CONTACT_EMAIL}`}
              variant="default"
              leftSection={<QuestionMarkIcon />}
              fullWidth
              className={styles.needHelpButton}
            >
              Need Help
            </Button>
          </Box>
        </Box>
      </Flex>
    </div>
  );
};
