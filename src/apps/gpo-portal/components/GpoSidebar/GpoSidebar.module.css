.sidebarRoot {
  width: 280px;
  height: 100vh;
  background: linear-gradient(180deg, #7a8ba8 0%, #1a3a6b 100%);
  border-right: 1px solid #e5e7eb;
  padding: 0 1rem;
  position: fixed;
  left: 0;
  top: 0;
  z-index: 100;
  color: #fff;
}

.sidebarRoot,
.sidebarRoot * {
  color: #fff;
}

.sidebarRoot .container.active,
.sidebarRoot .container.active *,
.sidebarRoot .container.active:visited,
.sidebarRoot .container.active:focus {
  background: rgba(255, 255, 255, 0.85);
  color: #161924;
}

.sidebarRoot .needHelpButton {
  background: rgba(255, 255, 255, 0.95);
  color: #161924;
  border: none;
  font-weight: 600;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
}

.sidebarRoot .needHelpButton span,
.sidebarRoot .needHelpButton svg path {
  fill: #161924;
  color: #161924;
}

.lock {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  gap: 1rem;
  color: #6b7280;
}
