import { Flex, Loader } from '@mantine/core';
import { Suspense, useEffect } from 'react';
import { Navigate, Outlet, useLocation, useNavigate } from 'react-router-dom';
import { ErrorBoundary } from 'react-error-boundary';

import { ErrorSection } from '@/components';
import { ProtectedLayout } from '@/apps/shop/Layouts/ProtectedLayout/ProtectedLayout';
import { useClinicStore } from '@/apps/shop/stores/useClinicStore';
import { useAccountStore } from '@/apps/shop/stores/useAccountStore';
import { SHOP_ROUTES_PATH } from '@/apps/shop/routes/routes';
import { CLINIC_NAV_LINKS, CLINIC_SETTING_LINKS } from '@/apps/shop/constants';

import { useCartStore } from '@/apps/shop/stores/useCartStore/useCartStore';
import { getActiveClinic } from '@/libs/clinics/utils/activeClinic';
import { MainSidebar } from '../../components/MainSidebar/MainSidebar';
import { ShopTopNavbar } from '../../components/ShopTopNavbar/ShopTopNavbar';
import { AppContentWrap } from '@/libs/ui/AppContentWrap/AppContentWrap';

interface ClinicLayoutProps {
  showCart?: boolean;
}
export const ClinicLayout = ({ showCart }: ClinicLayoutProps) => {
  const { getClinic, clinic } = useClinicStore();
  const { fetchCart } = useCartStore();
  const { activeClinic } = useAccountStore();
  const location = useLocation();
  const navigate = useNavigate();
  const localStorageActiveClinic = getActiveClinic();

  useEffect(() => {
    if (activeClinic && !activeClinic.hasAnyVendorConnected) {
      navigate(SHOP_ROUTES_PATH.vendors);
    }
  }, [location.pathname, navigate, clinic, activeClinic]);

  const loadInitialUserData = async () => {
    if (localStorageActiveClinic) {
      await getClinic(localStorageActiveClinic.id);
      await fetchCart();
    }
  };

  const navLinkGroups = [
    {
      title: 'Main menu',
      links: CLINIC_NAV_LINKS,
    },
    {
      title: 'Preferences',
      links: CLINIC_SETTING_LINKS,
    },
  ];

  if (!localStorageActiveClinic) {
    return <Navigate to={SHOP_ROUTES_PATH.home} />;
  }

  return (
    <ErrorBoundary fallback={<ErrorSection />}>
      <Suspense
        fallback={
          <div className="loaderRoot height100vh">
            <Loader size="3rem" />
          </div>
        }
      >
        <ProtectedLayout additionApiCall={loadInitialUserData}>
          <AppContentWrap>
            <Flex>
              <MainSidebar navLinkGroups={navLinkGroups} />
              <Flex direction="column" w="100%">
                <ShopTopNavbar showCart={showCart} />
                <ErrorBoundary fallback={<ErrorSection />}>
                  <Outlet />
                </ErrorBoundary>
              </Flex>
            </Flex>
          </AppContentWrap>
        </ProtectedLayout>
      </Suspense>
    </ErrorBoundary>
  );
};
