import { Box, Flex, Text } from '@mantine/core';
import { useForm } from 'react-hook-form';
import { yupResolver } from '@hookform/resolvers/yup';
import * as Yup from 'yup';
import { OnboardingStep } from '../OnboardingStep/OnboardingStep';
import { Input } from '@/libs/form/Input';
import { post } from '@/libs/utils/api';
import { useState } from 'react';
import { OnboardingStepFormWrapper } from '../OnboardingStep/OnboardingStepFormWrapper';
import { Button } from '@/libs/ui/Button/Button';
import { Link } from 'react-router-dom';
import { SHOP_ROUTES_PATH } from '@/apps/shop/routes/routes';
import { errorNotification } from '@/utils';

type SignUpRequest = {
  firstName: string;
  lastName: string;
  email: string;
  password: string;
  confirmPassword?: string;
};

type ApiErrorResponse = {
  data: {
    message: string;
    errors?: Record<string, string[]>;
  };
};

const SCHEMA = Yup.object().shape({
  firstName: Yup.string().required('First name is required'),
  lastName: Yup.string().required('Last name is required'),
  email: Yup.string().email('Invalid email').required('Email is required'),
  password: Yup.string()
    .min(8, 'Password must be at least 8 characters')
    .required('Password is required'),
  confirmPassword: Yup.string()
    .oneOf([Yup.ref('password')], 'Passwords must match')
    .required('Confirm password is required'),
});

interface CreatePasswordStepProps {
  inviteEmail: string;
  onComplete: VoidFunction;
  invitationToken: string | null;
  gpoLogoUrl: string | null;
}
export const CreatePasswordStep = ({
  inviteEmail,
  onComplete,
  gpoLogoUrl,
  invitationToken,
}: CreatePasswordStepProps) => {
  const [isLoading, setIsLoading] = useState(false);
  const {
    register,
    handleSubmit,
    formState: { errors },
    setError,
  } = useForm({
    resolver: yupResolver(SCHEMA),
    defaultValues: {
      firstName: '',
      lastName: '',
      email: inviteEmail,
      password: '',
      confirmPassword: '',
    },
  });

  const onSubmit = async (data: SignUpRequest) => {
    setIsLoading(true);
    try {
      await post({
        url: `/accounts/sign-up`,
        body: {
          ...data,
          name: `${data.firstName} ${data.lastName}`,
          accountType: 'clinic_account',
          ...(invitationToken ? { invitationToken } : {}),
        },
      });

      onComplete();
    } catch (error) {
      const apiError = error as ApiErrorResponse;

      if (apiError?.data?.errors) {
        Object.keys(apiError.data.errors).forEach((errorKey) => {
          if (errorKey in data) {
            const fieldKey = errorKey as keyof SignUpRequest;
            const messages = apiError.data.errors?.[fieldKey];
            if (messages && messages.length > 0) {
              setError(fieldKey, { message: messages[0] });
            }
          }
        });

        if (apiError?.data?.errors?.invitationToken?.[0]) {
          errorNotification(apiError.data.errors.invitationToken[0]);
        }
      } else {
        errorNotification();
      }
    }
    setIsLoading(false);
  };

  return (
    <OnboardingStep
      title="Create your password"
      subTitle="Discover exclusive offers, simplify your ordering process, and access tools designed to help your clinic growth."
      currentStep={1}
      totalSteps={3}
      gpoLogoUrl={gpoLogoUrl}
    >
      <OnboardingStepFormWrapper onSubmit={handleSubmit(onSubmit)}>
        <Text c="#344054" size="1rem" fw="500" mb="md">
          Your Info
        </Text>

        <Flex gap="1rem" mb="24px">
          <Box flex="1">
            <Input
              label="First Name"
              {...register('firstName')}
              error={errors.firstName?.message}
            />
          </Box>
          <Box flex="1">
            <Input
              label="Last Name"
              {...register('lastName')}
              error={errors.lastName?.message}
            />
          </Box>
        </Flex>

        <Box mb="24px">
          <Input
            label="Email"
            defaultValue={inviteEmail}
            {...register('email')}
            disabled={!!inviteEmail}
            error={errors.email?.message}
          />
        </Box>

        <Box mb="24px">
          <Input
            label="Password"
            type="password"
            {...register('password')}
            error={errors.password?.message}
          />
        </Box>

        <Box mb="24px">
          <Input
            label="Confirm Password"
            type="password"
            {...register('confirmPassword')}
            error={errors.confirmPassword?.message}
          />
        </Box>
      </OnboardingStepFormWrapper>
      <Box mt="2rem" w="100%">
        <Button type="submit" form="stepForm" loading={isLoading}>
          Next
        </Button>
      </Box>
      <Box mt="24px">
        <Text c="#333" size="16px">
          Already have an account?{' '}
          <Link
            to={SHOP_ROUTES_PATH.login}
            className="link"
            style={{ color: '#333' }}
          >
            Log in
          </Link>
        </Text>
      </Box>
    </OnboardingStep>
  );
};
